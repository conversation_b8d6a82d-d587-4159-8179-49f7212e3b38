using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Enums;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class BorrowsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public BorrowsController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetBorrowRecords()
    {
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var borrowRecordDtos = borrowRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = br.Status,
            StatusName = GetStatusName(br.Status),
            Notes = br.Notes,
            Fine = br.Fine,
            Quantity = br.Quantity,
            IsOverdue = br.IsOverdue,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt,
            BookshelfId = br.BookshelfId,
            LocationCode = br.LocationCode
        }).OrderByDescending(br => br.BorrowDate).ToList();

        return Ok(borrowRecordDtos);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<BorrowRecordDto>> GetBorrowRecord(int id)
    {
        var borrowRecord = await _unitOfWork.BorrowRecords.GetByIdAsync(id);
        if (borrowRecord == null)
        {
            return NotFound();
        }

        var book = await _unitOfWork.Books.GetByIdAsync(borrowRecord.BookId);
        var member = await _unitOfWork.Members.GetByIdAsync(borrowRecord.MemberId);
        
        // Lấy thông tin bookshelf nếu có
        string? bookshelfName = null;
        if (borrowRecord.BookshelfId.HasValue)
        {
            var bookshelf = await _unitOfWork.Bookshelves.GetByIdAsync(borrowRecord.BookshelfId.Value);
            bookshelfName = bookshelf?.Name;
        }

        var borrowRecordDto = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book?.Title ?? "Unknown",
            BookAuthor = book?.Author ?? "Unknown",
            MemberId = borrowRecord.MemberId,
            MemberName = member?.FullName ?? "Unknown",
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt,
            BookshelfId = borrowRecord.BookshelfId,
            BookshelfName = bookshelfName,
            LocationCode = borrowRecord.LocationCode
        };

        return Ok(borrowRecordDto);
    }

    [HttpPost]
    public async Task<ActionResult<BorrowRecordDto>> CreateBorrowRecord(CreateBorrowRecordDto createDto)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(createDto.BookId);
        if (book == null)
        {
            return BadRequest("Không tìm thấy sách");
        }

        // Kiểm tra số lượng sách có sẵn
        if (book.StockQuantity + book.OnShelfQuantity < 1)
        {
            return BadRequest($"Không có sách để mượn. Hiện chỉ có {book.StockQuantity + book.OnShelfQuantity} cuốn.");
        }

        // Validate member exists and is active
        var member = await _unitOfWork.Members.GetByIdAsync(createDto.MemberId);
        if (member == null)
        {
            return BadRequest("Không tìm thấy thành viên");
        }

        if (member.Status != MemberStatus.Active)
        {
            return BadRequest("Thành viên không hoạt động");
        }

        // Check if member has overdue books
        var memberOverdueBooks = await _unitOfWork.BorrowRecords.FindAsync(br =>
            br.MemberId == createDto.MemberId && 
            br.ReturnDate == null && 
            br.DueDate < DateTime.Now);

        if (memberOverdueBooks.Any())
        {
            return BadRequest("Thành viên có sách quá hạn chưa trả");
        }
        
        // Lấy thông tin vị trí sách (nếu sách đang ở trên kệ)
        int? bookshelfId = null;
        string? locationCode = null;
        
        if (book.BookshelfId.HasValue && book.OnShelfQuantity >= 1)
        {
            bookshelfId = book.BookshelfId;
            locationCode = book.LocationCode;
        }

        // Create borrow record
        var borrowRecord = new BorrowRecord
        {
            BookId = createDto.BookId,
            MemberId = createDto.MemberId,
            BorrowDate = DateTime.Now,
            DueDate = createDto.DueDate,
            Status = BorrowStatus.Borrowed,
            Notes = createDto.Notes,
            Quantity = 1, // Luôn mượn 1 cuốn
            CreatedAt = DateTime.Now,
            BookshelfId = bookshelfId,
            LocationCode = locationCode
        };

        await _unitOfWork.BorrowRecords.AddAsync(borrowRecord);
        
        // Sử dụng phương thức Borrow của Book entity để cập nhật số lượng
        bool success = book.Borrow(1); // Luôn mượn 1 cuốn
        if (!success)
        {
            return BadRequest("Không thể mượn sách do lỗi hệ thống");
        }
        
        await _unitOfWork.Books.UpdateAsync(book);
        await _unitOfWork.SaveChangesAsync();

        // Return the created record with full details
        var result = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book.Title,
            BookAuthor = book.Author,
            MemberId = borrowRecord.MemberId,
            MemberName = member.FullName,
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            Quantity = borrowRecord.Quantity,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt,
            BookshelfId = borrowRecord.BookshelfId,
            LocationCode = borrowRecord.LocationCode
        };

        return CreatedAtAction(nameof(GetBorrowRecord), new { id = borrowRecord.Id }, result);
    }

    [HttpPut("{id}/return")]
    public async Task<ActionResult<BorrowRecordDto>> ReturnBook(int id, ReturnBookDto returnDto)
    {
        if (id != returnDto.BorrowRecordId)
        {
            return BadRequest("ID không khớp");
        }

        var borrowRecord = await _unitOfWork.BorrowRecords.GetByIdAsync(id);
        if (borrowRecord == null)
        {
            return NotFound();
        }

        if (borrowRecord.ReturnDate.HasValue)
        {
            return BadRequest("Sách đã được trả");
        }

        var book = await _unitOfWork.Books.GetByIdAsync(borrowRecord.BookId);
        if (book == null)
        {
            return BadRequest("Không tìm thấy thông tin sách");
        }

        // Sử dụng phương thức ReturnBook của BorrowRecord
        bool success = borrowRecord.ReturnBook();
        if (!success)
        {
            return BadRequest("Không thể trả sách do lỗi hệ thống");
        }
        
        // Cập nhật ngày trả và ghi chú
        borrowRecord.ReturnDate = returnDto.ReturnDate;
        borrowRecord.Notes = returnDto.Notes ?? borrowRecord.Notes;
        borrowRecord.UpdatedAt = DateTime.Now;

        // Calculate fine if overdue (only if returned after due date)
        if (returnDto.ReturnDate > borrowRecord.DueDate)
        {
            var daysOverdue = (returnDto.ReturnDate - borrowRecord.DueDate).Days;
            decimal finePerDay = 5000; // 5000 VND per day
            borrowRecord.Fine = returnDto.Fine ?? (daysOverdue * finePerDay);
        }
        else
        {
            // Nếu trả đúng hạn hoặc trước hạn, không có phạt
            borrowRecord.Fine = returnDto.Fine ?? 0;
        }

        // Sử dụng phương thức Return của Book entity để cập nhật số lượng
        book.Return(); // Luôn trả 1 cuốn
        
        await _unitOfWork.Books.UpdateAsync(book);
        await _unitOfWork.BorrowRecords.UpdateAsync(borrowRecord);
        await _unitOfWork.SaveChangesAsync();

        // Return updated record
        var member = await _unitOfWork.Members.GetByIdAsync(borrowRecord.MemberId);
        
        // Lấy thông tin bookshelf nếu có
        string? bookshelfName = null;
        if (borrowRecord.BookshelfId.HasValue)
        {
            var bookshelf = await _unitOfWork.Bookshelves.GetByIdAsync(borrowRecord.BookshelfId.Value);
            bookshelfName = bookshelf?.Name;
        }
        
        var result = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book.Title,
            BookAuthor = book.Author,
            MemberId = borrowRecord.MemberId,
            MemberName = member?.FullName ?? "Unknown",
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            Quantity = borrowRecord.Quantity,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt,
            BookshelfId = borrowRecord.BookshelfId,
            BookshelfName = bookshelfName,
            LocationCode = borrowRecord.LocationCode
        };

        return Ok(result);
    }

    [HttpPost("{id}/renew")]
    public async Task<ActionResult<BorrowRecordDto>> RenewBook(int id, RenewBookDto renewDto)
    {
        var borrowRecord = await _unitOfWork.BorrowRecords.GetByIdAsync(id);
        if (borrowRecord == null)
        {
            return NotFound();
        }

        if (borrowRecord.ReturnDate.HasValue)
        {
            return BadRequest("Không thể gia hạn sách đã trả");
        }

        if (borrowRecord.IsOverdue)
        {
            return BadRequest("Không thể gia hạn sách quá hạn");
        }

        // Sử dụng phương thức Renew của BorrowRecord
        bool success = borrowRecord.Renew(renewDto.Days);
        if (!success)
        {
            return BadRequest("Không thể gia hạn sách");
        }
        
        borrowRecord.Notes = (borrowRecord.Notes ?? "") + $"\nGia hạn thêm {renewDto.Days} ngày vào {DateTime.Now:dd/MM/yyyy}.";
        borrowRecord.UpdatedAt = DateTime.Now;

        await _unitOfWork.BorrowRecords.UpdateAsync(borrowRecord);
        await _unitOfWork.SaveChangesAsync();

        // Return updated record
        var book = await _unitOfWork.Books.GetByIdAsync(borrowRecord.BookId);
        var member = await _unitOfWork.Members.GetByIdAsync(borrowRecord.MemberId);
        
        // Lấy thông tin bookshelf nếu có
        string? bookshelfName = null;
        if (borrowRecord.BookshelfId.HasValue)
        {
            var bookshelf = await _unitOfWork.Bookshelves.GetByIdAsync(borrowRecord.BookshelfId.Value);
            bookshelfName = bookshelf?.Name;
        }
        
        var result = new BorrowRecordDto
        {
            Id = borrowRecord.Id,
            BookId = borrowRecord.BookId,
            BookTitle = book?.Title ?? "Unknown",
            BookAuthor = book?.Author ?? "Unknown",
            MemberId = borrowRecord.MemberId,
            MemberName = member?.FullName ?? "Unknown",
            BorrowDate = borrowRecord.BorrowDate,
            DueDate = borrowRecord.DueDate,
            ReturnDate = borrowRecord.ReturnDate,
            Status = borrowRecord.Status,
            StatusName = GetStatusName(borrowRecord.Status),
            Notes = borrowRecord.Notes,
            Fine = borrowRecord.Fine,
            Quantity = borrowRecord.Quantity,
            IsOverdue = borrowRecord.IsOverdue,
            DaysOverdue = borrowRecord.DaysOverdue,
            CreatedAt = borrowRecord.CreatedAt,
            BookshelfId = borrowRecord.BookshelfId,
            BookshelfName = bookshelfName,
            LocationCode = borrowRecord.LocationCode
        };

        return Ok(result);
    }

    [HttpGet("overdue")]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetOverdueBooks()
    {
        var overdueRecords = await _unitOfWork.BorrowRecords.FindAsync(br =>
            br.ReturnDate == null && br.DueDate < DateTime.Now);

        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var overdueDtos = overdueRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = BorrowStatus.Overdue, // Override status for overdue
            StatusName = "Quá hạn",
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = true,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.DaysOverdue).ToList();

        return Ok(overdueDtos);
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> SearchBorrowRecords(
        [FromQuery] string? memberName,
        [FromQuery] string? bookTitle,
        [FromQuery] BorrowStatus? status,
        [FromQuery] bool? overdue)
    {
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var filteredRecords = borrowRecords.AsEnumerable();

        if (!string.IsNullOrEmpty(memberName))
        {
            filteredRecords = filteredRecords.Where(br =>
                memberDict.GetValueOrDefault(br.MemberId)?.FullName.Contains(memberName, StringComparison.OrdinalIgnoreCase) == true);
        }

        if (!string.IsNullOrEmpty(bookTitle))
        {
            filteredRecords = filteredRecords.Where(br =>
                bookDict.GetValueOrDefault(br.BookId)?.Title.Contains(bookTitle, StringComparison.OrdinalIgnoreCase) == true);
        }

        if (status.HasValue)
        {
            filteredRecords = filteredRecords.Where(br => br.Status == status.Value);
        }

        if (overdue.HasValue)
        {
            if (overdue.Value)
            {
                filteredRecords = filteredRecords.Where(br => br.IsOverdue);
            }
            else
            {
                filteredRecords = filteredRecords.Where(br => !br.IsOverdue);
            }
        }

        var borrowRecordDtos = filteredRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = br.Status,
            StatusName = GetStatusName(br.Status),
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = br.IsOverdue,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.BorrowDate).ToList();

        return Ok(borrowRecordDtos);
    }

    private static string GetStatusName(BorrowStatus status)
    {
        return status switch
        {
            BorrowStatus.Borrowed => "Đang mượn",
            BorrowStatus.Returned => "Đã trả",
            BorrowStatus.Overdue => "Quá hạn",
            BorrowStatus.Lost => "Mất sách",
            BorrowStatus.Renewed => "Đã gia hạn",
            _ => "Không xác định"
        };
    }
}