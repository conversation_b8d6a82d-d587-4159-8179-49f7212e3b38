using LibraryManagement.Core.Enums;

namespace LibraryManagement.Core.Entities;

public class BorrowRecord : BaseEntity
{
    public int BookId { get; set; }
    public int MemberId { get; set; }
    public DateTime BorrowDate { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? ReturnDate { get; set; }
    public BorrowStatus Status { get; set; }
    public string? Notes { get; set; }
    public decimal? Fine { get; set; }
    public int Quantity { get; set; } = 1; // Số lượng sách mượn (mặc định là 1)

    // Vị trí sách được mượn
    public int? BookshelfId { get; set; }
    public string? LocationCode { get; set; }
    
    public bool IsOverdue => DueDate < DateTime.Now && ReturnDate == null;
    public int DaysOverdue => IsOverdue ? Math.Max(0, (DateTime.Now - DueDate).Days) : 0;
    
    // Navigation properties
    public virtual Book Book { get; set; } = null!;
    public virtual Member Member { get; set; } = null!;
    public virtual Bookshelf? Bookshelf { get; set; }
    
    // Phương thức để trả sách
    public bool ReturnBook(bool returnToShelf = true)
    {
        if (ReturnDate != null)
            return false;
            
        ReturnDate = DateTime.Now;
        Status = BorrowStatus.Returned;
        
        return true;
    }
    
    // Phương thức để gia hạn
    public bool Renew(int days)
    {
        if (IsOverdue || Status == BorrowStatus.Returned)
            return false;
            
        DueDate = DueDate.AddDays(days);
        Status = BorrowStatus.Renewed;
        return true;
    }
} 