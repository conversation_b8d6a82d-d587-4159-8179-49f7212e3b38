import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Member, CreateMember, UpdateMember, MemberStatus, BorrowRecord } from '../models/member.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MemberService {
  private apiUrl = `${environment.apiUrl}/members`;

  constructor(private http: HttpClient) { }

  getMembers(): Observable<Member[]> {
    return this.http.get<Member[]>(this.apiUrl);
  }

  getMember(id: number): Observable<Member> {
    return this.http.get<Member>(`${this.apiUrl}/${id}`);
  }

  createMember(member: CreateMember): Observable<Member> {
    return this.http.post<Member>(this.apiUrl, member);
  }

  updateMember(id: number, member: UpdateMember): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, member);
  }

  deleteMember(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  searchMembers(
    searchTerm?: string,
    firstName?: string,
    lastName?: string,
    email?: string,
    status?: MemberStatus
  ): Observable<Member[]> {
    let params = new HttpParams();

    if (searchTerm) {
      params = params.set('searchTerm', searchTerm);
    }
    if (firstName) {
      params = params.set('firstName', firstName);
    }
    if (lastName) {
      params = params.set('lastName', lastName);
    }
    if (email) {
      params = params.set('email', email);
    }
    if (status !== undefined) {
      params = params.set('status', status.toString());
    }

    return this.http.get<Member[]>(`${this.apiUrl}/search`, { params });
  }

  getMemberBorrowHistory(id: number): Observable<BorrowRecord[]> {
    return this.http.get<BorrowRecord[]>(`${this.apiUrl}/${id}/borrow-history`);
  }
} 