using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class BooksController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public BooksController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<BookDto>>> GetBooks()
    {
        var books = await _unitOfWork.Books.GetAllAsync();
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);

        var bookDtos = books
            .OrderByDescending(book => book.CreatedAt) // Sắp xếp sách mới nhất lên đầu
            .Select(book => new BookDto
            {
                Id = book.Id,
                Title = book.Title,
                Author = book.Author,
                ISBN = book.ISBN,
                Publisher = book.Publisher,
                PublishedDate = book.PublishedDate,
                CategoryId = book.CategoryId,
                CategoryName = categoryDict.GetValueOrDefault(book.CategoryId, "Unknown"),
                Quantity = book.Quantity,
                StockQuantity = book.StockQuantity,
                OnShelfQuantity = book.OnShelfQuantity,
                BorrowedQuantity = book.BorrowedQuantity,
                Description = book.Description,
                ImageUrl = book.ImageUrl,
                Price = book.Price,
                CreatedAt = book.CreatedAt,
                UpdatedAt = book.UpdatedAt,
                BookshelfId = book.BookshelfId,
                LocationCode = book.LocationCode
            }).ToList();

        return Ok(bookDtos);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<BookDto>> GetBook(int id)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }

        var category = await _unitOfWork.Categories.GetByIdAsync(book.CategoryId);
        
        // Lấy thông tin bookshelf nếu có
        string? bookshelfName = null;
        if (book.BookshelfId.HasValue)
        {
            var bookshelf = await _unitOfWork.Bookshelves.GetByIdAsync(book.BookshelfId.Value);
            bookshelfName = bookshelf?.Name;
        }
        
        var bookDto = new BookDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            Publisher = book.Publisher,
            PublishedDate = book.PublishedDate,
            CategoryId = book.CategoryId,
            CategoryName = category?.Name ?? "Unknown",
            Quantity = book.Quantity,
            StockQuantity = book.StockQuantity,
            OnShelfQuantity = book.OnShelfQuantity,
            BorrowedQuantity = book.BorrowedQuantity,
            Description = book.Description,
            ImageUrl = book.ImageUrl,
            Price = book.Price,
            CreatedAt = book.CreatedAt,
            UpdatedAt = book.UpdatedAt,
            BookshelfId = book.BookshelfId,
            BookshelfName = bookshelfName,
            LocationCode = book.LocationCode,
            Location = book.BookshelfId.HasValue ? $"{bookshelfName} - {book.LocationCode}" : null
        };

        return Ok(bookDto);
    }

    [HttpPost]
    public async Task<ActionResult<BookDto>> CreateBook(CreateBookDto createBookDto)
    {
        var existingBook = await _unitOfWork.Books.FindAsync(b => b.ISBN == createBookDto.ISBN);
        if (existingBook.Any())
        {
            return BadRequest("ISBN đã tồn tại. Không thể tạo sách trùng mã ISBN.");
        }

        // Validate category exists
        var categoryExists = await _unitOfWork.Categories.ExistsAsync(createBookDto.CategoryId);
        if (!categoryExists)
        {
            return BadRequest("Invalid category ID");
        }

        var book = new Book
        {
            Title = createBookDto.Title,
            Author = createBookDto.Author,
            ISBN = createBookDto.ISBN,
            Publisher = createBookDto.Publisher,
            PublishedDate = createBookDto.PublishedDate,
            CategoryId = createBookDto.CategoryId,
            StockQuantity = createBookDto.StockQuantity, // Tất cả vào kho ban đầu
            OnShelfQuantity = 0, // Chưa có trên kệ
            BorrowedQuantity = 0, // Chưa có sách được mượn
            Description = createBookDto.Description,
            ImageUrl = createBookDto.ImageUrl,
            Price = createBookDto.Price
        };

        await _unitOfWork.Books.AddAsync(book);
        await _unitOfWork.SaveChangesAsync();

        var category = await _unitOfWork.Categories.GetByIdAsync(book.CategoryId);

        var bookDto = new BookDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            Publisher = book.Publisher,
            PublishedDate = book.PublishedDate,
            CategoryId = book.CategoryId,
            CategoryName = category?.Name ?? "Unknown",
            Quantity = book.Quantity,
            StockQuantity = book.StockQuantity,
            OnShelfQuantity = book.OnShelfQuantity,
            BorrowedQuantity = book.BorrowedQuantity,
            Description = book.Description,
            ImageUrl = book.ImageUrl,
            Price = book.Price,
            CreatedAt = book.CreatedAt,
            UpdatedAt = book.UpdatedAt
        };

        return CreatedAtAction(nameof(GetBook), new { id = book.Id }, bookDto);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateBook(int id, UpdateBookDto updateBookDto)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }

        var isbnConflict = await _unitOfWork.Books
            .FindAsync(b => b.ISBN == updateBookDto.ISBN && b.Id != id);
        if (isbnConflict.Any())
        {
            return BadRequest("ISBN đã được sử dụng cho một sách khác.");
        }

        // Validate category exists
        var categoryExists = await _unitOfWork.Categories.ExistsAsync(updateBookDto.CategoryId);
        if (!categoryExists)
        {
            return BadRequest("Invalid category ID");
        }

        // Cập nhật thông tin cơ bản
        book.Title = updateBookDto.Title;
        book.Author = updateBookDto.Author;
        book.ISBN = updateBookDto.ISBN;
        book.Publisher = updateBookDto.Publisher;
        book.PublishedDate = updateBookDto.PublishedDate;
        book.CategoryId = updateBookDto.CategoryId;
        book.Description = updateBookDto.Description;
        book.ImageUrl = updateBookDto.ImageUrl;
        book.Price = updateBookDto.Price;
        
        // Xử lý cập nhật số lượng
        // Chỉ cho phép cập nhật số lượng trong kho, không thay đổi số lượng trên kệ
        int stockDelta = updateBookDto.StockQuantity - book.StockQuantity;
        if (stockDelta > 0)
        {
            // Thêm sách vào kho
            book.AddToStock(stockDelta);
        }
        else if (stockDelta < 0)
        {
            // Giảm số lượng trong kho, nhưng không được âm
            book.StockQuantity = Math.Max(0, updateBookDto.StockQuantity);
        }
        
        // Cập nhật thông tin kệ nếu có
        if (updateBookDto.BookshelfId.HasValue && updateBookDto.BookshelfId != book.BookshelfId)
        {
            // Validate bookshelf exists
            var bookshelfExists = await _unitOfWork.Bookshelves.ExistsAsync(updateBookDto.BookshelfId.Value);
            if (!bookshelfExists)
            {
                return BadRequest("Invalid bookshelf ID");
            }
            
            book.BookshelfId = updateBookDto.BookshelfId;
            book.LocationCode = updateBookDto.LocationCode;
        }

        await _unitOfWork.Books.UpdateAsync(book);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteBook(int id)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }

        // Kiểm tra xem sách có đang được mượn không
        if (book.BorrowedQuantity > 0)
        {
            return BadRequest("Không thể xóa sách đang được mượn.");
        }

        await _unitOfWork.Books.DeleteAsync(book);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpPost("delete-multiple")]
    public async Task<IActionResult> DeleteMultipleBooks([FromBody] int[] bookIds)
    {
        if (bookIds == null || bookIds.Length == 0)
        {
            return BadRequest("No book IDs provided");
        }

        var books = new List<Book>();
        var errors = new List<string>();

        foreach (var id in bookIds)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(id);
            if (book == null)
            {
                errors.Add($"Book with ID {id} not found");
                continue;
            }

            // Kiểm tra xem sách có đang được mượn không
            if (book.BorrowedQuantity > 0)
            {
                errors.Add($"Book '{book.Title}' (ID: {id}) cannot be deleted because it is currently borrowed");
                continue;
            }

            books.Add(book);
        }

        if (errors.Any())
        {
            return BadRequest(new { Errors = errors });
        }

        foreach (var book in books)
        {
            await _unitOfWork.Books.DeleteAsync(book);
        }

        await _unitOfWork.SaveChangesAsync();

        return Ok(new { Message = $"Successfully deleted {books.Count} books" });
    }
    
    // DTO cho việc chuyển sách lên kệ
    public class MoveToShelfRequest
    {
        public int BookshelfId { get; set; }
        public string LocationCode { get; set; } = string.Empty;
        public int Quantity { get; set; }
    }

    // API endpoint để chuyển sách từ kho lên kệ
    [HttpPost("{id}/move-to-shelf")]
    public async Task<IActionResult> MoveToShelf(int id, MoveToShelfRequest request)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }
        
        // Validate bookshelf exists
        var bookshelfExists = await _unitOfWork.Bookshelves.ExistsAsync(request.BookshelfId);
        if (!bookshelfExists)
        {
            return BadRequest("Invalid bookshelf ID");
        }
        
        // Thực hiện chuyển sách
        bool success = book.MoveToShelf(request.Quantity, request.BookshelfId, request.LocationCode);
        if (!success)
        {
            return BadRequest("Không đủ sách trong kho để chuyển lên kệ");
        }
        
        await _unitOfWork.Books.UpdateAsync(book);
        await _unitOfWork.SaveChangesAsync();
        
        return Ok(new { Message = $"Đã chuyển {request.Quantity} sách lên kệ thành công" });
    }

    [HttpGet("in-storage")]
    public async Task<IActionResult> GetBooksInStorage()
    {
        var books = await _unitOfWork.Books.FindAsync(b => b.StockQuantity > 0);
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);

        var bookDtos = books
            .OrderByDescending(book => book.CreatedAt) // Sắp xếp sách mới nhất lên đầu
            .Select(book => new BookDto
            {
                Id = book.Id,
                Title = book.Title,
                Author = book.Author,
                ISBN = book.ISBN,
                CategoryId = book.CategoryId,
                CategoryName = categoryDict.GetValueOrDefault(book.CategoryId, "Unknown"),
                Quantity = book.Quantity,
                StockQuantity = book.StockQuantity,
                OnShelfQuantity = book.OnShelfQuantity,
                BorrowedQuantity = book.BorrowedQuantity,
                ImageUrl = book.ImageUrl
            }).ToList();

        return Ok(bookDtos);
    }

    [HttpGet("on-shelf")]
    public async Task<IActionResult> GetBooksOnShelf()
    {
        var books = await _unitOfWork.Books
            .FindAsync(b => b.BookshelfId != null && b.OnShelfQuantity > 0);
            
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);
        
        var bookshelves = await _unitOfWork.Bookshelves.GetAllAsync();
        var bookshelfDict = bookshelves.ToDictionary(s => s.Id, s => s.Name);
        
        var result = books.Select(book => new BookOnShelfDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            CategoryId = book.CategoryId,
            CategoryName = categoryDict.GetValueOrDefault(book.CategoryId, "Unknown"),
            OnShelfQuantity = book.OnShelfQuantity,
            StockQuantity = book.OnShelfQuantity, // Để tương thích với frontend
            BookshelfId = book.BookshelfId ?? 0,
            BookshelfName = book.BookshelfId.HasValue ? bookshelfDict.GetValueOrDefault(book.BookshelfId.Value, "Unknown") : "Unknown",
            LocationCode = book.LocationCode,
            Location = book.BookshelfId.HasValue ? $"{bookshelfDict.GetValueOrDefault(book.BookshelfId.Value, "Unknown")}" + (book.LocationCode != null ? $" - {book.LocationCode}" : "") : "Không xác định"
        }).ToList();
        
        return Ok(result);
    }
    
    [HttpGet("search-general")]
    public async Task<ActionResult<IEnumerable<BookDto>>> SearchBooksGeneral([FromQuery] string q)
    {
        if (string.IsNullOrEmpty(q))
        {
            return await GetBooks();
        }
        
        var searchTerm = q.ToLower();
        
        var books = await _unitOfWork.Books.GetAllWithCategoriesAsync();
        var result = books.Where(b => 
            b.Title.ToLower().Contains(searchTerm) || 
            b.Author.ToLower().Contains(searchTerm) || 
            b.ISBN.ToLower().Contains(searchTerm) ||
            b.Category.Name.ToLower().Contains(searchTerm)
        ).ToList();
        
        var bookDtos = result
            .OrderByDescending(book => book.CreatedAt) // Sắp xếp sách mới nhất lên đầu
            .Select(book => new BookDto
            {
                Id = book.Id,
                Title = book.Title,
                Author = book.Author,
                ISBN = book.ISBN,
                Publisher = book.Publisher,
                PublishedDate = book.PublishedDate,
                CategoryId = book.CategoryId,
                CategoryName = book.Category?.Name ?? "Unknown",
                Quantity = book.Quantity,
                StockQuantity = book.StockQuantity,
                OnShelfQuantity = book.OnShelfQuantity,
                BorrowedQuantity = book.BorrowedQuantity,
                Description = book.Description,
                ImageUrl = book.ImageUrl,
                Price = book.Price,
                CreatedAt = book.CreatedAt,
                UpdatedAt = book.UpdatedAt,
                BookshelfId = book.BookshelfId,
                LocationCode = book.LocationCode
            }).ToList();
        
        return Ok(bookDtos);
    }
}